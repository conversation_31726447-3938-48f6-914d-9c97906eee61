import {
  IsNotEmpty,
  IsNumber,
  IsObject,
  ValidateNested,
  IsArray,
  IsString,
  Matches,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class TimeSlotDto {
  @ApiProperty({
    description: 'Gi<PERSON> bắt đầu (format: HH:mm)',
    example: '09:00',
  })
  @IsString({ message: 'Giờ bắt đầu phải là chuỗi' })
  @IsNotEmpty({ message: 'Giờ bắt đầu không được để trống' })
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Giờ bắt đầu phải có định dạng HH:mm',
  })
  start: string;

  @ApiProperty({
    description: 'Giờ kết thúc (format: HH:mm)',
    example: '11:00',
  })
  @IsString({ message: '<PERSON><PERSON><PERSON> kết thúc phải là chuỗi' })
  @IsNotEmpty({ message: 'Gi<PERSON> kết thúc không được để trống' })
  @Matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Giờ kết thúc phải có định dạng HH:mm',
  })
  end: string;
}

export class WeeklyScheduleDto {
  @ApiProperty({
    description: 'Lịch thứ 2',
    type: [TimeSlotDto],
    required: false,
  })
  @IsArray({ message: 'Lịch thứ 2 phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  monday?: TimeSlotDto[];

  @ApiProperty({
    description: 'Lịch thứ 3',
    type: [TimeSlotDto],
    required: false,
  })
  @IsArray({ message: 'Lịch thứ 3 phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  tuesday?: TimeSlotDto[];

  @ApiProperty({
    description: 'Lịch thứ 4',
    type: [TimeSlotDto],
    required: false,
  })
  @IsArray({ message: 'Lịch thứ 4 phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  wednesday?: TimeSlotDto[];

  @ApiProperty({
    description: 'Lịch thứ 5',
    type: [TimeSlotDto],
    required: false,
  })
  @IsArray({ message: 'Lịch thứ 5 phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  thursday?: TimeSlotDto[];

  @ApiProperty({
    description: 'Lịch thứ 6',
    type: [TimeSlotDto],
    required: false,
  })
  @IsArray({ message: 'Lịch thứ 6 phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  friday?: TimeSlotDto[];

  @ApiProperty({
    description: 'Lịch thứ 7',
    type: [TimeSlotDto],
    required: false,
  })
  @IsArray({ message: 'Lịch thứ 7 phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  saturday?: TimeSlotDto[];

  @ApiProperty({
    description: 'Lịch chủ nhật',
    type: [TimeSlotDto],
    required: false,
  })
  @IsArray({ message: 'Lịch chủ nhật phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => TimeSlotDto)
  sunday?: TimeSlotDto[];
}

export class CreateMonthlyScheduleDto {
  @ApiProperty({
    description: 'Tháng (1-12)',
    example: 12,
    minimum: 1,
    maximum: 12,
  })
  @IsNumber({}, { message: 'Tháng phải là số' })
  @IsNotEmpty({ message: 'Tháng không được để trống' })
  @Min(1, { message: 'Tháng phải từ 1 đến 12' })
  @Max(12, { message: 'Tháng phải từ 1 đến 12' })
  month: number;

  @ApiProperty({
    description: 'Năm',
    example: 2024,
    minimum: 2024,
  })
  @IsNumber({}, { message: 'Năm phải là số' })
  @IsNotEmpty({ message: 'Năm không được để trống' })
  @Min(2024, { message: 'Năm phải từ 2024 trở đi' })
  year: number;

  @ApiProperty({
    description: 'Dữ liệu lịch làm việc theo tuần',
    type: WeeklyScheduleDto,
  })
  @IsObject({ message: 'Dữ liệu lịch phải là object' })
  @ValidateNested()
  @Type(() => WeeklyScheduleDto)
  scheduleData: WeeklyScheduleDto;
}
