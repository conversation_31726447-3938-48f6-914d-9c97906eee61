import { Injectable, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaClient, ScheduleStatus, RequestStatus, RequestType } from '@prisma/client';
import {
  CreateMonthlyScheduleDto,
  CreateScheduleRequestDto,
  ProcessScheduleRequestDto,
  QueryScheduleDto,
  QueryScheduleRequestDto,
  ConflictAction,
} from '../dto';

@Injectable()
export class ScheduleService {
  constructor(private readonly prisma: PrismaClient) {}

  // Tạo lịch cố định hàng tháng
  async createMonthlySchedule(doctorId: string, dto: CreateMonthlyScheduleDto) {
    // Kiểm tra xem bác sĩ đã có lịch cho tháng này chưa
    const existingSchedule = await this.prisma.doctorSchedule.findUnique({
      where: {
        doctorId_month_year: {
          doctorId,
          month: dto.month,
          year: dto.year,
        },
      },
    });

    if (existingSchedule) {
      throw new BadRequestException(
        `<PERSON><PERSON><PERSON> sĩ đã có lịch cho tháng ${dto.month}/${dto.year}. <PERSON><PERSON> lòng cập nhật lịch hiện tại hoặc tạo yêu cầu thay đổi.`
      );
    }

    // Validate schedule data
    this.validateScheduleData(dto.scheduleData);

    return this.prisma.doctorSchedule.create({
      data: {
        doctorId,
        month: dto.month,
        year: dto.year,
        scheduleData: dto.scheduleData as any,
        status: ScheduleStatus.PENDING,
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  // Tạo yêu cầu thay đổi lịch đột xuất
  async createScheduleRequest(doctorId: string, dto: CreateScheduleRequestDto) {
    // Validate request date (phải là ngày trong tương lai)
    const requestDate = new Date(dto.requestDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (requestDate < today) {
      throw new BadRequestException('Không thể tạo yêu cầu cho ngày trong quá khứ');
    }

    // Validate time slots for non-full-day requests
    if (dto.requestType !== RequestType.FULL_DAY_OFF) {
      if (!dto.startTime || !dto.endTime) {
        throw new BadRequestException('Giờ bắt đầu và kết thúc là bắt buộc cho loại yêu cầu này');
      }
      
      if (dto.startTime >= dto.endTime) {
        throw new BadRequestException('Giờ bắt đầu phải nhỏ hơn giờ kết thúc');
      }
    }

    // Generate unique request code
    const requestCode = `REQ${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    return this.prisma.scheduleRequest.create({
      data: {
        requestCode,
        doctorId,
        requestType: dto.requestType,
        requestDate,
        startTime: dto.startTime,
        endTime: dto.endTime,
        reason: dto.reason,
        description: dto.description,
        status: RequestStatus.PENDING,
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  // Lấy danh sách lịch cố định
  async getDoctorSchedules(doctorId: string, query: QueryScheduleDto) {
    const where: any = { doctorId };

    if (query.month) where.month = query.month;
    if (query.year) where.year = query.year;
    if (query.status) where.status = query.status;

    return this.prisma.doctorSchedule.findMany({
      where,
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        clinicAdmin: {
          include: {
            user: true,
          },
        },
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  // Lấy danh sách yêu cầu thay đổi lịch
  async getScheduleRequests(doctorId: string, query: QueryScheduleRequestDto) {
    const where: any = { doctorId };

    if (query.requestType) where.requestType = query.requestType;
    if (query.status) where.status = query.status;

    const skip = ((query.page || 1) - 1) * (query.limit || 10);

    const [requests, total] = await Promise.all([
      this.prisma.scheduleRequest.findMany({
        where,
        include: {
          doctor: {
            include: {
              user: true,
            },
          },
          clinicAdmin: {
            include: {
              user: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: query.limit || 10,
      }),
      this.prisma.scheduleRequest.count({ where }),
    ]);

    return {
      data: requests,
      pagination: {
        page: query.page || 1,
        limit: query.limit || 10,
        total,
        totalPages: Math.ceil(total / (query.limit || 10)),
      },
    };
  }

  // ===== CLINIC ADMIN METHODS =====

  // Lấy danh sách lịch cố định của tất cả bác sĩ trong phòng khám
  async getClinicSchedules(clinicId: string, query: QueryScheduleDto) {
    const where: any = {
      doctor: { clinicId },
    };

    if (query.month) where.month = query.month;
    if (query.year) where.year = query.year;
    if (query.status) where.status = query.status;
    if (query.doctorId) where.doctorId = query.doctorId;

    return this.prisma.doctorSchedule.findMany({
      where,
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        clinicAdmin: {
          include: {
            user: true,
          },
        },
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  // Lấy danh sách yêu cầu thay đổi lịch của tất cả bác sĩ trong phòng khám
  async getClinicScheduleRequests(clinicId: string, query: QueryScheduleRequestDto) {
    const where: any = {
      doctor: { clinicId },
    };

    if (query.requestType) where.requestType = query.requestType;
    if (query.status) where.status = query.status;
    if (query.doctorId) where.doctorId = query.doctorId;

    const skip = ((query.page || 1) - 1) * (query.limit || 10);

    const [requests, total] = await Promise.all([
      this.prisma.scheduleRequest.findMany({
        where,
        include: {
          doctor: {
            include: {
              user: true,
            },
          },
          clinicAdmin: {
            include: {
              user: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: query.limit || 10,
      }),
      this.prisma.scheduleRequest.count({ where }),
    ]);

    return {
      data: requests,
      pagination: {
        page: query.page || 1,
        limit: query.limit || 10,
        total,
        totalPages: Math.ceil(total / (query.limit || 10)),
      },
    };
  }

  // Duyệt lịch cố định hàng tháng
  async approveMonthlySchedule(scheduleId: string, clinicAdminId: string) {
    const schedule = await this.prisma.doctorSchedule.findUnique({
      where: { id: scheduleId },
      include: {
        doctor: {
          include: {
            clinic: true,
          },
        },
      },
    });

    if (!schedule) {
      throw new NotFoundException('Không tìm thấy lịch làm việc');
    }

    if (schedule.status !== ScheduleStatus.PENDING) {
      throw new BadRequestException('Chỉ có thể duyệt lịch đang chờ xử lý');
    }

    // Verify clinic admin belongs to the same clinic
    const clinicAdmin = await this.prisma.clinicAdmin.findUnique({
      where: { id: clinicAdminId },
    });

    if (!clinicAdmin || clinicAdmin.clinicId !== schedule.doctor.clinicId) {
      throw new ForbiddenException('Không có quyền duyệt lịch của phòng khám khác');
    }

    return this.prisma.doctorSchedule.update({
      where: { id: scheduleId },
      data: {
        status: ScheduleStatus.APPROVED,
        approvedBy: clinicAdminId,
        approvedAt: new Date(),
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        clinicAdmin: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  // Từ chối lịch cố định hàng tháng
  async rejectMonthlySchedule(scheduleId: string, clinicAdminId: string, reason?: string) {
    const schedule = await this.prisma.doctorSchedule.findUnique({
      where: { id: scheduleId },
      include: {
        doctor: {
          include: {
            clinic: true,
          },
        },
      },
    });

    if (!schedule) {
      throw new NotFoundException('Không tìm thấy lịch làm việc');
    }

    if (schedule.status !== ScheduleStatus.PENDING) {
      throw new BadRequestException('Chỉ có thể từ chối lịch đang chờ xử lý');
    }

    // Verify clinic admin belongs to the same clinic
    const clinicAdmin = await this.prisma.clinicAdmin.findUnique({
      where: { id: clinicAdminId },
    });

    if (!clinicAdmin || clinicAdmin.clinicId !== schedule.doctor.clinicId) {
      throw new ForbiddenException('Không có quyền từ chối lịch của phòng khám khác');
    }

    return this.prisma.doctorSchedule.update({
      where: { id: scheduleId },
      data: {
        status: ScheduleStatus.REJECTED,
        approvedBy: clinicAdminId,
        approvedAt: new Date(),
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        clinicAdmin: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  // Xử lý yêu cầu thay đổi lịch
  async processScheduleRequest(
    requestId: string,
    clinicAdminId: string,
    dto: ProcessScheduleRequestDto
  ) {
    const request = await this.prisma.scheduleRequest.findUnique({
      where: { id: requestId },
      include: {
        doctor: {
          include: {
            clinic: true,
          },
        },
      },
    });

    if (!request) {
      throw new NotFoundException('Không tìm thấy yêu cầu');
    }

    if (request.status !== RequestStatus.PENDING) {
      throw new BadRequestException('Chỉ có thể xử lý yêu cầu đang chờ xử lý');
    }

    // Verify clinic admin belongs to the same clinic
    const clinicAdmin = await this.prisma.clinicAdmin.findUnique({
      where: { id: clinicAdminId },
    });

    if (!clinicAdmin || clinicAdmin.clinicId !== request.doctor.clinicId) {
      throw new ForbiddenException('Không có quyền xử lý yêu cầu của phòng khám khác');
    }

    // Check for appointment conflicts if request is CANCEL_HOURS or FULL_DAY_OFF
    let affectedAppointments: any[] = [];
    if (
      dto.status === RequestStatus.APPROVED &&
      (request.requestType === RequestType.CANCEL_HOURS || request.requestType === RequestType.FULL_DAY_OFF)
    ) {
      affectedAppointments = await this.checkAppointmentConflicts(request);

      if (affectedAppointments.length > 0 && !dto.conflictAction) {
        throw new BadRequestException(
          'Có lịch hẹn bệnh nhân trong thời gian này. Vui lòng chọn hành động xử lý xung đột.'
        );
      }
    }

    // Process the request
    const updatedRequest = await this.prisma.scheduleRequest.update({
      where: { id: requestId },
      data: {
        status: dto.status,
        processedBy: clinicAdminId,
        processedAt: new Date(),
        adminNote: dto.adminNote,
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        clinicAdmin: {
          include: {
            user: true,
          },
        },
      },
    });

    // Handle appointment conflicts if approved
    if (dto.status === RequestStatus.APPROVED && affectedAppointments.length > 0) {
      await this.handleAppointmentConflicts(affectedAppointments, dto.conflictAction!);
    }

    return updatedRequest;
  }

  // Kiểm tra xung đột với lịch hẹn bệnh nhân
  private async checkAppointmentConflicts(request: any) {
    const whereCondition: any = {
      doctorId: request.doctorId,
      date: request.requestDate,
      status: { not: 'CANCELLED' }, // Không tính các appointment đã hủy
    };

    // Nếu là hủy giờ cụ thể, kiểm tra trong khoảng thời gian đó
    if (request.requestType === RequestType.CANCEL_HOURS) {
      whereCondition.OR = [
        {
          AND: [
            { startTime: { lte: request.endTime } },
            { endTime: { gte: request.startTime } },
          ],
        },
      ];
    }

    return this.prisma.appointment.findMany({
      where: whereCondition,
      include: {
        patient: {
          include: {
            user: true,
          },
        },
        service: true,
      },
    });
  }

  // Xử lý xung đột với lịch hẹn
  private async handleAppointmentConflicts(appointments: any[], action: ConflictAction) {
    switch (action) {
      case ConflictAction.CANCEL_APPOINTMENTS:
        // Hủy tất cả lịch hẹn bị ảnh hưởng
        await this.prisma.appointment.updateMany({
          where: {
            id: { in: appointments.map(apt => apt.id) },
          },
          data: {
            status: 'CANCELLED',
          },
        });
        // TODO: Gửi thông báo cho bệnh nhân
        break;

      case ConflictAction.RESCHEDULE_APPOINTMENTS:
        // TODO: Implement logic để tự động đề xuất lịch mới
        // Hiện tại chỉ đánh dấu cần reschedule
        await this.prisma.appointment.updateMany({
          where: {
            id: { in: appointments.map(apt => apt.id) },
          },
          data: {
            status: 'NEEDS_RESCHEDULE',
          },
        });
        break;

      case ConflictAction.REJECT_REQUEST:
        // Không làm gì với appointments, request sẽ bị từ chối
        break;
    }
  }

  // Validate schedule data
  private validateScheduleData(scheduleData: any) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    for (const day of days) {
      if (scheduleData[day] && Array.isArray(scheduleData[day])) {
        for (const slot of scheduleData[day]) {
          if (!slot.start || !slot.end) {
            throw new BadRequestException(`Thiếu thông tin giờ làm việc cho ${day}`);
          }

          if (slot.start >= slot.end) {
            throw new BadRequestException(`Giờ bắt đầu phải nhỏ hơn giờ kết thúc cho ${day}`);
          }
        }

        // Check for overlapping time slots
        const slots = scheduleData[day].sort((a: any, b: any) => a.start.localeCompare(b.start));
        for (let i = 0; i < slots.length - 1; i++) {
          if (slots[i].end > slots[i + 1].start) {
            throw new BadRequestException(`Có xung đột thời gian trong lịch ${day}`);
          }
        }
      }
    }
  }
}
