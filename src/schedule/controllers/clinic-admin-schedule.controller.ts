import {
  Controller,
  Get,
  Put,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../login/jwt-auth.guard';
import { RolesGuard } from '../../rbac/roles.guard';
import { Roles } from '../../rbac/roles.decorator';
import { Role } from '../../rbac/roles.enum';
import { ScheduleService } from '../services/schedule.service';
import {
  ProcessScheduleRequestDto,
  QueryScheduleDto,
  QueryScheduleRequestDto,
} from '../dto';

@ApiTags('Clinic Admin Schedule')
@Controller('clinic-admin/schedule')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class ClinicAdminScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Get('monthly')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách lịch cố định của tất cả bác sĩ trong phòng khám' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách lịch cố định',
  })
  async getClinicSchedules(
    @Request() req: any,
    @Query() query: QueryScheduleDto,
  ) {
    const clinicId = req.user.clinicAdmin?.clinicId;
    if (!clinicId) {
      throw new Error('Không tìm thấy thông tin phòng khám');
    }

    return this.scheduleService.getClinicSchedules(clinicId, query);
  }

  @Get('requests')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Lấy danh sách yêu cầu thay đổi lịch của tất cả bác sĩ trong phòng khám' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách yêu cầu thay đổi lịch',
  })
  async getClinicScheduleRequests(
    @Request() req: any,
    @Query() query: QueryScheduleRequestDto,
  ) {
    const clinicId = req.user.clinicAdmin?.clinicId;
    if (!clinicId) {
      throw new Error('Không tìm thấy thông tin phòng khám');
    }

    return this.scheduleService.getClinicScheduleRequests(clinicId, query);
  }

  @Put('monthly/:scheduleId/approve')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Duyệt lịch cố định hàng tháng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lịch cố định đã được duyệt',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy lịch làm việc',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền duyệt lịch của phòng khám khác',
  })
  async approveMonthlySchedule(
    @Request() req: any,
    @Param('scheduleId') scheduleId: string,
  ) {
    const clinicAdminId = req.user.clinicAdmin?.id;
    if (!clinicAdminId) {
      throw new Error('Không tìm thấy thông tin clinic admin');
    }

    return this.scheduleService.approveMonthlySchedule(scheduleId, clinicAdminId);
  }

  @Put('monthly/:scheduleId/reject')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Từ chối lịch cố định hàng tháng' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lịch cố định đã bị từ chối',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy lịch làm việc',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền từ chối lịch của phòng khám khác',
  })
  async rejectMonthlySchedule(
    @Request() req: any,
    @Param('scheduleId') scheduleId: string,
    @Body() body: { reason?: string },
  ) {
    const clinicAdminId = req.user.clinicAdmin?.id;
    if (!clinicAdminId) {
      throw new Error('Không tìm thấy thông tin clinic admin');
    }

    return this.scheduleService.rejectMonthlySchedule(
      scheduleId,
      clinicAdminId,
      body.reason,
    );
  }

  @Put('requests/:requestId/process')
  @Roles(Role.CLINIC_ADMIN)
  @ApiOperation({ summary: 'Xử lý yêu cầu thay đổi lịch' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Yêu cầu đã được xử lý',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy yêu cầu',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Có xung đột với lịch hẹn bệnh nhân',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền xử lý yêu cầu của phòng khám khác',
  })
  async processScheduleRequest(
    @Request() req: any,
    @Param('requestId') requestId: string,
    @Body() dto: ProcessScheduleRequestDto,
  ) {
    const clinicAdminId = req.user.clinicAdmin?.id;
    if (!clinicAdminId) {
      throw new Error('Không tìm thấy thông tin clinic admin');
    }

    return this.scheduleService.processScheduleRequest(
      requestId,
      clinicAdminId,
      dto,
    );
  }
}
