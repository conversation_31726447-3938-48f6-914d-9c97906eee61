import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../login/jwt-auth.guard';
import { RolesGuard } from '../../rbac/roles.guard';
import { Roles } from '../../rbac/roles.decorator';
import { Role } from '../../rbac/roles.enum';
import { ScheduleService } from '../services/schedule.service';
import {
  CreateMonthlyScheduleDto,
  CreateScheduleRequestDto,
  QueryScheduleDto,
  QueryScheduleRequestDto,
} from '../dto';

@ApiTags('Doctor Schedule')
@Controller('doctor/schedule')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class DoctorScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Post('monthly')
  @Roles(Role.DOCTOR)
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> lịch cố định hàng tháng' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Lịch cố định đã được tạo thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ hoặc đã tồn tại lịch cho tháng này',
  })
  async createMonthlySchedule(
    @Request() req: any,
    @Body() dto: CreateMonthlyScheduleDto,
  ) {
    const doctorId = req.user.doctor?.id;
    if (!doctorId) {
      throw new Error('Không tìm thấy thông tin bác sĩ');
    }

    return this.scheduleService.createMonthlySchedule(doctorId, dto);
  }

  @Post('request')
  @Roles(Role.DOCTOR)
  @ApiOperation({ summary: 'Tạo yêu cầu thay đổi lịch đột xuất' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Yêu cầu thay đổi lịch đã được tạo thành công',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
  })
  async createScheduleRequest(
    @Request() req: any,
    @Body() dto: CreateScheduleRequestDto,
  ) {
    const doctorId = req.user.doctor?.id;
    if (!doctorId) {
      throw new Error('Không tìm thấy thông tin bác sĩ');
    }

    return this.scheduleService.createScheduleRequest(doctorId, dto);
  }

  @Get('monthly')
  @Roles(Role.DOCTOR)
  @ApiOperation({ summary: 'Lấy danh sách lịch cố định của bác sĩ' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách lịch cố định',
  })
  async getMySchedules(
    @Request() req: any,
    @Query() query: QueryScheduleDto,
  ) {
    const doctorId = req.user.doctor?.id;
    if (!doctorId) {
      throw new Error('Không tìm thấy thông tin bác sĩ');
    }

    return this.scheduleService.getDoctorSchedules(doctorId, query);
  }

  @Get('requests')
  @Roles(Role.DOCTOR)
  @ApiOperation({ summary: 'Lấy danh sách yêu cầu thay đổi lịch của bác sĩ' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách yêu cầu thay đổi lịch',
  })
  async getMyScheduleRequests(
    @Request() req: any,
    @Query() query: QueryScheduleRequestDto,
  ) {
    const doctorId = req.user.doctor?.id;
    if (!doctorId) {
      throw new Error('Không tìm thấy thông tin bác sĩ');
    }

    return this.scheduleService.getScheduleRequests(doctorId, query);
  }
}
