import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class UserContextMiddleware implements NestMiddleware {
  constructor(private readonly prisma: PrismaClient) {}

  async use(req: Request, _res: Response, next: NextFunction) {
    // Chỉ xử lý nếu user đã đượ<PERSON> authenticate
    if (req.user && (req.user as any).id) {
      const userId = (req.user as any).id;
      const role = (req.user as any).role;

      try {
        // Load thông tin doctor nếu user là DOCTOR
        if (role === 'DOCTOR') {
          const doctor = await this.prisma.doctor.findUnique({
            where: { userId },
            include: {
              clinic: true,
            },
          });
          if (doctor) {
            (req.user as any).doctor = doctor;
          }
        }

        // Load thông tin clinicAdmin nếu user là CLINIC_ADMIN
        if (role === 'CLINIC_ADMIN') {
          const clinicAdmin = await this.prisma.clinicAdmin.findUnique({
            where: { userId },
            include: {
              clinic: true,
            },
          });
          if (clinicAdmin) {
            (req.user as any).clinicAdmin = clinicAdmin;
          }
        }

        // Load thông tin patient nếu user là PATIENT
        if (role === 'PATIENT') {
          const patient = await this.prisma.patient.findUnique({
            where: { userId },
          });
          if (patient) {
            (req.user as any).patient = patient;
          }
        }

        // Load thông tin receptionist nếu user là RECEPTIONIST
        if (role === 'RECEPTIONIST') {
          const receptionist = await this.prisma.receptionist.findUnique({
            where: { userId },
            include: {
              clinic: true,
            },
          });
          if (receptionist) {
            (req.user as any).receptionist = receptionist;
          }
        }
      } catch (error) {
        console.error('Error loading user context:', error);
        // Không throw error để không block request
      }
    }

    next();
  }
}
