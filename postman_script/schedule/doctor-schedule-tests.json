{"info": {"name": "Doctor Schedule Management Tests", "description": "Test collection for doctor schedule management endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{doctorAccessT<PERSON>}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "doctorAccessToken", "value": "", "type": "string"}], "item": [{"name": "1. Create Monthly Schedule", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"month\": 12,\n  \"year\": 2024,\n  \"scheduleData\": {\n    \"monday\": [\n      {\n        \"start\": \"09:00\",\n        \"end\": \"11:00\"\n      },\n      {\n        \"start\": \"14:00\",\n        \"end\": \"17:00\"\n      }\n    ],\n    \"tuesday\": [\n      {\n        \"start\": \"08:00\",\n        \"end\": \"12:00\"\n      }\n    ],\n    \"wednesday\": [\n      {\n        \"start\": \"09:00\",\n        \"end\": \"11:00\"\n      },\n      {\n        \"start\": \"14:00\",\n        \"end\": \"16:00\"\n      }\n    ],\n    \"thursday\": [],\n    \"friday\": [\n      {\n        \"start\": \"08:00\",\n        \"end\": \"12:00\"\n      },\n      {\n        \"start\": \"13:00\",\n        \"end\": \"17:00\"\n      }\n    ],\n    \"saturday\": [\n      {\n        \"start\": \"08:00\",\n        \"end\": \"12:00\"\n      }\n    ],\n    \"sunday\": []\n  }\n}"}, "url": {"raw": "{{baseUrl}}/doctor/schedule/monthly", "host": ["{{baseUrl}}"], "path": ["doctor", "schedule", "monthly"]}}, "response": []}, {"name": "2. Create Schedule Request - Add Hours", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requestType\": \"ADD_HOURS\",\n  \"requestDate\": \"2024-12-15\",\n  \"startTime\": \"18:00\",\n  \"endTime\": \"20:00\",\n  \"reason\": \"<PERSON><PERSON> ca cấp cứu cần hỗ trợ\",\n  \"description\": \"<PERSON><PERSON><PERSON> nhân cần phẫu thuật khẩn cấp, cần thêm giờ để hỗ trợ\"\n}"}, "url": {"raw": "{{baseUrl}}/doctor/schedule/request", "host": ["{{baseUrl}}"], "path": ["doctor", "schedule", "request"]}}, "response": []}, {"name": "3. Create Schedule Request - Cancel Hours", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requestType\": \"CANCEL_HOURS\",\n  \"requestDate\": \"2024-12-20\",\n  \"startTime\": \"14:00\",\n  \"endTime\": \"17:00\",\n  \"reason\": \"<PERSON><PERSON> việc gia đình đột xuất\",\n  \"description\": \"Cần về quê gấp do gia đình có việc\"\n}"}, "url": {"raw": "{{baseUrl}}/doctor/schedule/request", "host": ["{{baseUrl}}"], "path": ["doctor", "schedule", "request"]}}, "response": []}, {"name": "4. Create Schedule Request - Full Day Off", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requestType\": \"FULL_DAY_OFF\",\n  \"requestDate\": \"2024-12-25\",\n  \"reason\": \"Nghỉ lễ <PERSON>áng sinh\",\n  \"description\": \"Xin nghỉ lễ <PERSON>áng sinh\"\n}"}, "url": {"raw": "{{baseUrl}}/doctor/schedule/request", "host": ["{{baseUrl}}"], "path": ["doctor", "schedule", "request"]}}, "response": []}, {"name": "5. Get My Monthly Schedules", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/doctor/schedule/monthly?month=12&year=2024&status=PENDING", "host": ["{{baseUrl}}"], "path": ["doctor", "schedule", "monthly"], "query": [{"key": "month", "value": "12"}, {"key": "year", "value": "2024"}, {"key": "status", "value": "PENDING"}]}}, "response": []}, {"name": "6. Get My Schedule Requests", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/doctor/schedule/requests?status=PENDING&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["doctor", "schedule", "requests"], "query": [{"key": "status", "value": "PENDING"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}]}