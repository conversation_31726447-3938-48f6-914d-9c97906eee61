{"info": {"_postman_id": "revita-auth-api-collection-001", "name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Collection test các API authentication cho Revita Backend."}, "item": [{"name": "Đ<PERSON>ng nhập Google (Google OAuth2)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"<GOOGLE_AUTHORIZATION_CODE>\"\n}"}, "url": {"raw": "{{base_url}}/auth/google/token", "host": ["{{base_url}}"], "path": ["auth", "google", "token"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> nhập t<PERSON>n thống (Email/SĐT & Mật khẩu)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Làm mới token (Refresh Token)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"<REFRESH_TOKEN>\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> thông tin người dùng (Me)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <ACCESS_TOKEN>"}], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:3000"}]}