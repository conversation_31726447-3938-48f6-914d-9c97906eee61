{"info": {"_postman_id": "revita-user-management-api-collection", "name": "Revita User Management API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Admin - List all users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/users", "host": ["{{base_url}}"], "path": ["admin", "users"]}}}, {"name": "Admin - Get user by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/users/{{userId}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{userId}}"]}}}, {"name": "Admin - Create user", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON>\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"gender\": \"male\",\n  \"address\": \"123 Main St\",\n  \"role\": \"DOCTOR\",\n  \"password\": \"password123\",\n  \"clinicId\": \"{{clinicId}}\"\n}"}, "url": {"raw": "{{base_url}}/admin/users", "host": ["{{base_url}}"], "path": ["admin", "users"]}}}, {"name": "Admin - Update user", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/admin/users/{{userId}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{userId}}"]}}}, {"name": "Admin - Delete user", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/users/{{userId}}", "host": ["{{base_url}}"], "path": ["admin", "users", "{{userId}}"]}}}, {"name": "ClinicAdmin - List doctors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{clinic_admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/clinics/{{clinicId}}/doctors", "host": ["{{base_url}}"], "path": ["clinics", "{{clinicId}}", "doctors"]}}}, {"name": "ClinicAdmin - Create doctor", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{clinic_admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON> <PERSON>\",\n  \"dateOfBirth\": \"1980-01-01\",\n  \"gender\": \"male\",\n  \"address\": \"456 Clinic St\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/clinics/{{clinicId}}/doctors", "host": ["{{base_url}}"], "path": ["clinics", "{{clinicId}}", "doctors"]}}}, {"name": "ClinicAdmin - Update doctor", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{clinic_admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Dr. <PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/clinics/{{clinicId}}/doctors/{{doctorId}}", "host": ["{{base_url}}"], "path": ["clinics", "{{clinicId}}", "doctors", "{{doctorId}}"]}}}, {"name": "ClinicAdmin - Delete doctor", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{clinic_admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/clinics/{{clinicId}}/doctors/{{doctorId}}", "host": ["{{base_url}}"], "path": ["clinics", "{{clinicId}}", "doctors", "{{doctorId}}"]}}}, {"name": "ClinicAdmin - List receptionists", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{clinic_admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/clinics/{{clinicId}}/receptionists", "host": ["{{base_url}}"], "path": ["clinics", "{{clinicId}}", "receptionists"]}}}, {"name": "ClinicAdmin - Create receptionist", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{clinic_admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Receptionist A\",\n  \"dateOfBirth\": \"1995-01-01\",\n  \"gender\": \"female\",\n  \"address\": \"789 Reception St\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/clinics/{{clinicId}}/receptionists", "host": ["{{base_url}}"], "path": ["clinics", "{{clinicId}}", "receptionists"]}}}, {"name": "Receptionist - Register patient", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{receptionist_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Patient A\",\n  \"dateOfBirth\": \"2000-01-01\",\n  \"gender\": \"male\",\n  \"address\": \"123 Patient St\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/patients", "host": ["{{base_url}}"], "path": ["patients"]}}}, {"name": "Receptionist - Book appointment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{receptionist_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"bookerId\": \"{{receptionistId}}\",\n  \"patientId\": \"{{patientId}}\",\n  \"clinicId\": \"{{clinicId}}\",\n  \"specialtyId\": \"{{specialtyId}}\",\n  \"doctorId\": \"{{doctorId}}\",\n  \"serviceId\": \"{{serviceId}}\",\n  \"status\": \"BOOKED\",\n  \"date\": \"2025-07-03T10:00:00.000Z\",\n  \"startTime\": \"10:00\",\n  \"endTime\": \"10:30\"\n}"}, "url": {"raw": "{{base_url}}/appointments", "host": ["{{base_url}}"], "path": ["appointments"]}}}, {"name": "Doctor - View appointments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{doctor_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/doctors/{{doctorId}}/appointments", "host": ["{{base_url}}"], "path": ["doctors", "{{doctorId}}", "appointments"]}}}, {"name": "Doctor - Create medical record", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{doctor_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"patientId\": \"{{patientId}}\",\n  \"templateId\": \"{{templateId}}\",\n  \"content\": \"Medical record content\"\n}"}, "url": {"raw": "{{base_url}}/doctors/{{doctorId}}/medical-records", "host": ["{{base_url}}"], "path": ["doctors", "{{doctorId}}", "medical-records"]}}}], "variable": [{"key": "base_url", "value": "http://localhost:3000"}, {"key": "admin_token", "value": ""}, {"key": "clinic_admin_token", "value": ""}, {"key": "receptionist_token", "value": ""}, {"key": "doctor_token", "value": ""}, {"key": "patient_token", "value": ""}, {"key": "clinicId", "value": ""}, {"key": "userId", "value": ""}, {"key": "doctorId", "value": ""}, {"key": "receptionistId", "value": ""}, {"key": "patientId", "value": ""}, {"key": "specialtyId", "value": ""}, {"key": "serviceId", "value": ""}, {"key": "templateId", "value": ""}]}