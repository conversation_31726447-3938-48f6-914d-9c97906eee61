# Hệ thống Quản lý Lịch Làm việc <PERSON> sĩ

## Tổng quan

Đã hoàn thành việc xây dựng hệ thống quản lý lịch làm việc của bác sĩ theo quy trình nghiệp vụ được yêu cầu, bao gồm:

1. **Lịch cố định hàng tháng**: <PERSON><PERSON><PERSON> sĩ gửi lịch trước cuối tháng, admin duyệt
2. **Yêu cầu đột xuất**: <PERSON><PERSON><PERSON> sĩ có thể tạo yêu cầu thêm giờ, h<PERSON>y giờ, nghỉ nguyên ngày
3. **Xử lý xung đột**: Hệ thống kiểm tra và xử lý xung đột với lịch hẹn bệnh nhân

## Cấu trúc Database

### Models mới được thêm:

#### 1. DoctorSchedule (Lịch cố định hàng tháng)
```prisma
model DoctorSchedule {
  id          String      @id @default(uuid())
  doctorId    String      @map("doctor_id")
  month       Int         // Tháng (1-12)
  year        Int         // Năm
  scheduleData Json       @map("schedule_data") // Dữ liệu lịch theo tuần
  status      ScheduleStatus @default(PENDING)
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  approvedAt  DateTime?   @map("approved_at")
  approvedBy  String?     @map("approved_by") // ID của clinic admin duyệt
  
  @@unique([doctorId, month, year]) // Mỗi bác sĩ chỉ có 1 lịch cố định/tháng
}
```

#### 2. ScheduleRequest (Yêu cầu thay đổi lịch)
```prisma
model ScheduleRequest {
  id            String      @id @default(uuid())
  requestCode   String      @unique @map("request_code")
  doctorId      String      @map("doctor_id")
  requestType   RequestType @map("request_type")
  status        RequestStatus @default(PENDING)
  requestDate   DateTime    @map("request_date")
  startTime     String?     @map("start_time")
  endTime       String?     @map("end_time")
  reason        String?
  description   String?
  createdAt     DateTime    @default(now()) @map("created_at")
  processedAt   DateTime?   @map("processed_at")
  processedBy   String?     @map("processed_by")
  adminNote     String?     @map("admin_note")
}
```

#### 3. Enums mới:
- `ScheduleStatus`: PENDING, APPROVED, REJECTED
- `RequestType`: MONTHLY_SCHEDULE, ADD_HOURS, CANCEL_HOURS, FULL_DAY_OFF
- `RequestStatus`: PENDING, APPROVED, REJECTED

## API Endpoints

### Endpoints cho Bác sĩ (Doctor)

#### 1. Tạo lịch cố định hàng tháng
```
POST /doctor/schedule/monthly
```
**Body:**
```json
{
  "month": 12,
  "year": 2024,
  "scheduleData": {
    "monday": [{"start": "09:00", "end": "11:00"}],
    "tuesday": [{"start": "08:00", "end": "12:00"}],
    // ... các ngày khác
  }
}
```

#### 2. Tạo yêu cầu thay đổi lịch đột xuất
```
POST /doctor/schedule/request
```
**Body:**
```json
{
  "requestType": "ADD_HOURS", // hoặc "CANCEL_HOURS", "FULL_DAY_OFF"
  "requestDate": "2024-12-15",
  "startTime": "18:00", // không cần cho FULL_DAY_OFF
  "endTime": "20:00",   // không cần cho FULL_DAY_OFF
  "reason": "Có ca cấp cứu cần hỗ trợ",
  "description": "Mô tả chi tiết"
}
```

#### 3. Xem lịch cố định của mình
```
GET /doctor/schedule/monthly?month=12&year=2024&status=PENDING
```

#### 4. Xem yêu cầu thay đổi lịch của mình
```
GET /doctor/schedule/requests?status=PENDING&page=1&limit=10
```

### Endpoints cho Clinic Admin

#### 1. Xem tất cả lịch cố định trong phòng khám
```
GET /clinic-admin/schedule/monthly?month=12&year=2024&doctorId=uuid
```

#### 2. Xem tất cả yêu cầu thay đổi lịch trong phòng khám
```
GET /clinic-admin/schedule/requests?status=PENDING&doctorId=uuid&page=1&limit=10
```

#### 3. Duyệt lịch cố định hàng tháng
```
PUT /clinic-admin/schedule/monthly/:scheduleId/approve
```

#### 4. Từ chối lịch cố định hàng tháng
```
PUT /clinic-admin/schedule/monthly/:scheduleId/reject
```
**Body:**
```json
{
  "reason": "Lý do từ chối"
}
```

#### 5. Xử lý yêu cầu thay đổi lịch
```
PUT /clinic-admin/schedule/requests/:requestId/process
```
**Body:**
```json
{
  "status": "APPROVED", // hoặc "REJECTED"
  "adminNote": "Ghi chú của admin",
  "conflictAction": "CANCEL_APPOINTMENTS", // khi có xung đột
  "affectedAppointments": ["appointment-id-1", "appointment-id-2"]
}
```

## Tính năng chính

### 1. Validation và Business Logic
- Kiểm tra xung đột thời gian trong lịch làm việc
- Validate format thời gian (HH:mm)
- Không cho phép tạo yêu cầu cho ngày trong quá khứ
- Mỗi bác sĩ chỉ có 1 lịch cố định cho mỗi tháng

### 2. Xử lý xung đột với lịch hẹn bệnh nhân
Khi admin duyệt yêu cầu CANCEL_HOURS hoặc FULL_DAY_OFF:
- **CANCEL_APPOINTMENTS**: Hủy tất cả lịch hẹn bị ảnh hưởng
- **RESCHEDULE_APPOINTMENTS**: Đánh dấu cần dời lịch
- **REJECT_REQUEST**: Từ chối yêu cầu để giữ nguyên lịch hẹn

### 3. Phân quyền và bảo mật
- JWT authentication cho tất cả endpoints
- Bác sĩ chỉ có thể xem/tạo lịch của mình
- Clinic admin chỉ có thể quản lý lịch của bác sĩ trong phòng khám của mình
- Middleware tự động load thông tin doctor/clinicAdmin vào request

### 4. Audit Trail
- Lưu thông tin người duyệt và thời gian duyệt
- Lưu ghi chú của admin khi xử lý yêu cầu
- Tracking trạng thái thay đổi

## Cấu trúc Code

```
src/schedule/
├── controllers/
│   ├── doctor-schedule.controller.ts
│   └── clinic-admin-schedule.controller.ts
├── services/
│   └── schedule.service.ts
├── dto/
│   ├── create-monthly-schedule.dto.ts
│   ├── create-schedule-request.dto.ts
│   ├── process-schedule-request.dto.ts
│   ├── query-schedule.dto.ts
│   └── index.ts
├── middleware/
│   └── user-context.middleware.ts
└── schedule.module.ts
```

## Test Scripts

Đã tạo bộ test Postman hoàn chỉnh:
- `postman_script/schedule/doctor-schedule-tests.json`
- `postman_script/schedule/clinic-admin-schedule-tests.json`
- `postman_script/schedule/README.md`

## Migration Database

Đã tạo migration: `20250711124553_add_doctor_schedule_management`

## Cách sử dụng

1. **Import Postman collections** từ thư mục `postman_script/schedule/`
2. **Cấu hình variables**: baseUrl, accessToken
3. **Lấy JWT token** bằng cách login với tài khoản doctor/clinic_admin
4. **Chạy test scenarios** theo thứ tự trong README

## Lưu ý

- Tất cả thời gian sử dụng format 24h (HH:mm)
- Ngày sử dụng format ISO (YYYY-MM-DD)
- Hệ thống hỗ trợ pagination cho danh sách yêu cầu
- Có thể filter theo nhiều tiêu chí (tháng, năm, trạng thái, bác sĩ)
- Middleware tự động load context user để tối ưu performance
